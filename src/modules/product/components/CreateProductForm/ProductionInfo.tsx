import { useQuery } from "@tanstack/react-query";
import { useStore } from "@tanstack/react-store";
import { Plus, Trash2 } from "lucide-react";
import { CategoryCode } from "~/category/service/model/category";
import { useService } from "~/config/context/serviceProvider";
import { withForm } from "~/core/components/form/form";
import { measurementUnitOptions } from "~/modules/measurement-unit/hooks/measurement-unit-options";
import { productOptionsByCategoryCode } from "../../hooks/product-options";
import { ProductionType } from "../../store/productionInfo";
import { defaultValues } from "./defaultValues";

const ProductionInfo = withForm({
	defaultValues,
	render: ({ form }) => {
		const service = useService();
		const { data: materialsData = [] } = useQuery(
			productOptionsByCategoryCode(service, CategoryCode.MATERIALS),
		);
		const { data: measurementUnits = [] } = useQuery(
			measurementUnitOptions(service),
		);

		function getMeasurementUnitName(materialId: string) {
			const measurementId = materialsData.find(
				(mat) => mat.id === materialId,
			)?.measurementUnitID;

			return measurementId
				? measurementUnits.find((unit) => unit.id === measurementId)?.name
				: null;
		}

		return (
			<>
				<h2 className="card-title">Información del Producto</h2>
				<fieldset className="fieldset">
					<form.AppField
						name="productionInfo.productionType"
						children={({ state, handleChange }) => (
							<div className="flex flex-col gap-2">
								<label className="cursor-pointer">
									<input
										type="radio"
										name="productionType"
										className="radio radio-primary"
										checked={state.value === ProductionType.UNIT}
										onChange={() => handleChange(ProductionType.UNIT)}
									/>
									<span className="label-text ml-2">Por Unidad</span>
								</label>
								<label className="cursor-pointer">
									<input
										type="radio"
										name="productionType"
										className="radio radio-primary"
										checked={state.value === ProductionType.BULK}
										onChange={() => handleChange(ProductionType.BULK)}
									/>
									<span className="label-text ml-2">A Granel</span>
								</label>
							</div>
						)}
					/>
					<form.Subscribe
						selector={(state) => state.values.productionInfo?.productionType}
						children={(productionType) => (
							<form.AppField name="productionInfo.materials" mode="array">
								{(field) => {
									const materials = field.state.value;

									return productionType === ProductionType.BULK ? (
										<div className="mt-2 space-y-4">
											<button
												type="button"
												className="btn btn-primary btn-sm w-fit"
												onClick={() => {
													field.pushValue({
														productId: "",
														quantity: 1,
													});
												}}
											>
												<Plus size={16} />
												Agregar Material
											</button>
											{materials ? (
												materials.length === 0 ? (
													<div className="py-8 text-center text-gray-500">
														No hay materiales agregados. Haz clic en "Agregar
														Material" para comenzar.
													</div>
												) : (
													<table className="table">
														<thead>
															<tr>
																<th>Material</th>
																<th>Cantidad</th>
																<th>Acciones</th>
															</tr>
														</thead>
														<tbody>
															{materials.map((material, index) => (
																<tr key={material.productId}>
																	<td>
																		<form.AppField
																			name={`productionInfo.materials[${index}].productId`}
																			children={({ FSComboBoxField }) => (
																				<FSComboBoxField
																					placeholder="Seleccionar producto"
																					options={materialsData.map((mat) => ({
																						value: mat.id,
																						label: mat.name,
																					}))}
																				/>
																			)}
																		/>
																	</td>
																	<td>
																		<div className="flex items-center gap-2">
																			<form.AppField
																				name={`productionInfo.materials[${index}].quantity`}
																				children={({ FSTextField }) => (
																					<FSTextField
																						placeholder="0"
																						type="number"
																					/>
																				)}
																			/>
																			<form.Subscribe
																				selector={(state) =>
																					state.values.productionInfo
																						?.materials[index]?.productId
																				}
																				children={(productId) =>
																					productId
																						? getMeasurementUnitName(productId)
																						: null
																				}
																			/>
																		</div>
																	</td>
																	<td>
																		<button
																			type="button"
																			className="btn btn-error btn-sm"
																			onClick={() => field.removeValue(index)}
																		>
																			<Trash2 size={16} />
																		</button>
																	</td>
																</tr>
															))}
														</tbody>
													</table>
												)
											) : null}
										</div>
									) : null;
								}}
							</form.AppField>
						)}
					/>
				</fieldset>
			</>
		);
	},
});

export default ProductionInfo;
