import { Schema } from "effect";

export const WarehouseType = Schema.Literal("physical", "digital");
export type WarehouseType = typeof WarehouseType.Type;

export const WarehouseCategory = Schema.Literal(
	"provider",
	"client",
	"lost",
	"storage",
	"custom",
);
export type WarehouseCategory = typeof WarehouseCategory.Type;

export const Warehouse = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: WarehouseType,
	category: WarehouseCategory,
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	isActive: Schema.Boolean,
	isSystemWarehouse: Schema.Boolean,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Warehouse = typeof Warehouse.Type;

export const CreateWarehouse = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	type: WarehouseType,
	category: WarehouseCategory,
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	isActive: Schema.Boolean,
	isSystemWarehouse: Schema.Boolean,
});
export type CreateWarehouse = typeof CreateWarehouse.Type;

export const UpdateWarehouse = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: WarehouseType,
	category: WarehouseCategory,
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	isActive: Schema.Boolean,
	isSystemWarehouse: Schema.Boolean,
});
export type UpdateWarehouse = typeof UpdateWarehouse.Type;
