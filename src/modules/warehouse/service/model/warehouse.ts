import { Schema } from "effect";

export const Warehouse = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: Schema.String,
	category: Schema.String,
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	isActive: Schema.Boolean,
	isSystemWarehouse: Schema.Boolean,
	createdAt: Schema.NullOr(Schema.String),
	updatedAt: Schema.NullOr(Schema.String),
	deletedAt: Schema.NullOr(Schema.String),
});
export type Warehouse = typeof Warehouse.Type;

export const CreateWarehouse = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	type: Schema.String,
	category: Schema.String,
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	isActive: Schema.Boolean,
	isSystemWarehouse: Schema.Boolean,
});
export type CreateWarehouse = typeof CreateWarehouse.Type;

export const UpdateWarehouse = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: Schema.String,
	category: Schema.String,
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	isActive: Schema.Boolean,
	isSystemWarehouse: Schema.Boolean,
});
export type UpdateWarehouse = typeof UpdateWarehouse.Type;
