import { Schema } from "effect";
import {
	CreateWarehouse,
	UpdateWarehouse,
	Warehouse,
	WarehouseCategory,
	WarehouseType,
} from "../../model/warehouse";

export const WarehouseApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: WarehouseType,
	category: WarehouseCategory,
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	is_active: Schema.Boolean,
	is_system_warehouse: Schema.Boolean,
	created_at: Schema.NullOr(Schema.String),
	updated_at: Schema.NullOr(Schema.String),
	deleted_at: Schema.NullOr(Schema.String),
});

export const WarehouseFromApi = Schema.transform(WarehouseApi, Warehouse, {
	strict: true,
	decode: (warehouseApi) => ({
		...warehouseApi,
		isActive: warehouseApi.is_active,
		isSystemWarehouse: warehouseApi.is_system_warehouse,
		createdAt: warehouseApi.created_at,
		updatedAt: warehouseApi.updated_at,
		deletedAt: warehouseApi.deleted_at,
	}),
	encode: (warehouse) => ({
		...warehouse,
		is_active: warehouse.isActive,
		is_system_warehouse: warehouse.isSystemWarehouse,
		created_at: warehouse.createdAt,
		updated_at: warehouse.updatedAt,
		deleted_at: warehouse.deletedAt,
	}),
});

export const WarehouseListFromApi = Schema.transform(
	Schema.mutable(Schema.NullishOr(Schema.Array(WarehouseFromApi))),
	Schema.mutable(Schema.Array(Warehouse)),
	{
		strict: true,
		decode: (warehouseApiList) => (warehouseApiList ? warehouseApiList : []),
		encode: (warehouseList) => warehouseList,
	},
);

export const CreateWarehouseApi = Schema.Struct({
	name: Schema.String,
	code: Schema.String,
	type: WarehouseType,
	category: WarehouseCategory,
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	is_active: Schema.Boolean,
	is_system_warehouse: Schema.Boolean,
});

export const CreateWarehouseApiFromCreateWarehouse = Schema.transform(
	CreateWarehouse,
	CreateWarehouseApi,
	{
		strict: true,
		decode: (createWarehouse) => ({
			...createWarehouse,
			is_active: createWarehouse.isActive,
			is_system_warehouse: createWarehouse.isSystemWarehouse,
		}),
		encode: (createWarehouseApi) => ({
			...createWarehouseApi,
			isActive: createWarehouseApi.is_active,
			isSystemWarehouse: createWarehouseApi.is_system_warehouse,
		}),
	},
);

export const UpdateWarehouseApi = Schema.Struct({
	id: Schema.String,
	name: Schema.String,
	code: Schema.String,
	type: WarehouseType,
	category: WarehouseCategory,
	description: Schema.NullOr(Schema.String),
	address: Schema.NullOr(Schema.String),
	is_active: Schema.Boolean,
	is_system_warehouse: Schema.Boolean,
});

export const UpdateWarehouseApiFromUpdateWarehouse = Schema.transform(
	UpdateWarehouse,
	UpdateWarehouseApi,
	{
		strict: true,
		decode: (updateWarehouse) => ({
			...updateWarehouse,
			is_active: updateWarehouse.isActive,
			is_system_warehouse: updateWarehouse.isSystemWarehouse,
		}),
		encode: (updateWarehouseApi) => ({
			...updateWarehouseApi,
			isActive: updateWarehouseApi.is_active,
			isSystemWarehouse: updateWarehouseApi.is_system_warehouse,
		}),
	},
);

export const CreateWarehouseApiResponse = Schema.Struct({
	id: Schema.String,
});
