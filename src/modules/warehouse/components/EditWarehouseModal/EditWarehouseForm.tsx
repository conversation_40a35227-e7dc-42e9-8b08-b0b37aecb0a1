import { Hash, Tag, Building, MapPin, FileText, Settings } from "lucide-react";
import { useService } from "src/config/context/serviceProvider";
import CloseModal from "src/core/components/CloseModal";
import { AppRuntime } from "src/core/service/utils/runtimes";
import { cn } from "src/core/utils/classes";
import type { EditWarehouseModalProps } from "./use-edit-warehouse-modal";
import useEditWarehouseModal from "./use-edit-warehouse-modal";

export default function EditWarehouseForm({
	isOpen,
	setIsOpen,
	warehouse,
}: EditWarehouseModalProps) {
	const { warehouse: warehouseService } = useService();
	const { form, handleClose } = useEditWarehouseModal({
		isOpen,
		setIsOpen,
		warehouse,
	});

	return (
		<div className={cn("modal", isOpen && "modal-open")}>
			<div className="modal-box max-w-2xl">
				<CloseModal onClose={handleClose} />
				<h3 className="font-bold text-lg">Editar Almacén</h3>
				<form
					onSubmit={(e) => {
						e.preventDefault();
						form.handleSubmit();
					}}
				>
					<form.AppForm>
						<fieldset className="fieldset">
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<form.AppField
									name="name"
									validators={{
										onChangeAsyncDebounceMs: 500,
										onChangeAsync: async ({ value }) => {
											if (!value || value.trim() === "" || value === warehouse.name) {
												return undefined;
											}
											try {
												await AppRuntime.runPromise(warehouseService.validateName(value));
												return undefined;
											} catch (e) {
												return [{ message: "El nombre ya existe" }];
											}
										},
									}}
									children={({ FSTextField }) => (
										<FSTextField
											label="Nombre"
											placeholder="Nombre del almacén"
											prefixComponent={<Tag size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="code"
									validators={{
										onChangeAsyncDebounceMs: 500,
										onChangeAsync: async ({ value }) => {
											if (!value || value.trim() === "" || value === warehouse.code) {
												return undefined;
											}
											try {
												await AppRuntime.runPromise(warehouseService.validateCode(value));
												return undefined;
											} catch (e) {
												return [{ message: "El código ya existe" }];
											}
										},
									}}
									children={({ FSTextField }) => (
										<FSTextField
											label="Código"
											placeholder="Código del almacén"
											prefixComponent={<Hash size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="type"
									children={({ FSTextField }) => (
										<FSTextField
											label="Tipo"
											placeholder="Tipo de almacén"
											prefixComponent={<Building size={16} />}
										/>
									)}
								/>
								<form.AppField
									name="category"
									children={({ FSTextField }) => (
										<FSTextField
											label="Categoría"
											placeholder="Categoría del almacén"
											prefixComponent={<Settings size={16} />}
										/>
									)}
								/>
							</div>
							<form.AppField
								name="description"
								children={({ FSTextAreaField }) => (
									<FSTextAreaField
										label="Descripción"
										placeholder="Descripción del almacén"
										prefixComponent={<FileText size={16} />}
									/>
								)}
							/>
							<form.AppField
								name="address"
								children={({ FSTextAreaField }) => (
									<FSTextAreaField
										label="Dirección"
										placeholder="Dirección del almacén"
										prefixComponent={<MapPin size={16} />}
									/>
								)}
							/>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<form.AppField
									name="isActive"
									children={({ FSCheckboxField }) => (
										<FSCheckboxField
											label="Activo"
										/>
									)}
								/>
								<form.AppField
									name="isSystemWarehouse"
									children={({ FSCheckboxField }) => (
										<FSCheckboxField
											label="Almacén del sistema"
										/>
									)}
								/>
							</div>
						</fieldset>
						<div className="modal-action">
							<form.SubscribeButton
								label="Actualizar"
								className="btn btn-primary"
							/>
						</div>
					</form.AppForm>
				</form>
			</div>
		</div>
	);
}
