import { Edit, Trash2 } from "lucide-react";
import { useState } from "react";
import { toast } from "react-toastify";
import { getErrorResult } from "src/core/utils/effectErrors";
import useDeleteWarehouse from "../../hooks/use-delete-warehouse";
import type { Warehouse } from "../../service/model/warehouse";
import EditWarehouseModal from "../EditWarehouseModal";

interface Props {
	warehouses: Warehouse[];
}

export default function Table({ warehouses }: Props) {
	const [editingWarehouse, setEditingWarehouse] = useState<string | null>(null);
	const { mutate: deleteWarehouse } = useDeleteWarehouse();

	const handleDelete = (id: string, name: string) => {
		if (confirm(`¿Está seguro de eliminar el almacén "${name}"?`)) {
			deleteWarehouse(id, {
				onSuccess: () => {
					toast.success("Almacén eliminado");
				},
				onError: (error) => {
					const { error: err } = getErrorResult(error);
					toast.error(err.message);
				},
			});
		}
	};

	return (
		<>
			<div className="overflow-x-auto">
				<table className="table table-zebra">
					<thead>
						<tr>
							<th>Nombre</th>
							<th>Código</th>
							<th>Tipo</th>
							<th>Categoría</th>
							<th>Estado</th>
							<th>Sistema</th>
							<th>Acciones</th>
						</tr>
					</thead>
					<tbody>
						{warehouses.map((warehouse) => (
							<tr key={warehouse.id}>
								<td>
									<div>
										<div className="font-bold">{warehouse.name}</div>
										{warehouse.description && (
											<div className="text-sm opacity-50">{warehouse.description}</div>
										)}
									</div>
								</td>
								<td>{warehouse.code}</td>
								<td>{warehouse.type}</td>
								<td>{warehouse.category}</td>
								<td>
									<div className={`badge ${warehouse.isActive ? 'badge-success' : 'badge-error'}`}>
										{warehouse.isActive ? 'Activo' : 'Inactivo'}
									</div>
								</td>
								<td>
									<div className={`badge ${warehouse.isSystemWarehouse ? 'badge-info' : 'badge-ghost'}`}>
										{warehouse.isSystemWarehouse ? 'Sistema' : 'Normal'}
									</div>
								</td>
								<td>
									<div className="flex gap-2">
										<button
											type="button"
											className="btn btn-ghost btn-xs"
											onClick={() => setEditingWarehouse(warehouse.id)}
										>
											<Edit size={14} />
										</button>
										<button
											type="button"
											className="btn btn-ghost btn-xs text-error"
											onClick={() => handleDelete(warehouse.id, warehouse.name)}
										>
											<Trash2 size={14} />
										</button>
									</div>
								</td>
							</tr>
						))}
					</tbody>
				</table>
			</div>
			{editingWarehouse && (
				<EditWarehouseModal
					isOpen={!!editingWarehouse}
					setIsOpen={(open) => !open && setEditingWarehouse(null)}
					id={editingWarehouse}
				/>
			)}
		</>
	);
}
