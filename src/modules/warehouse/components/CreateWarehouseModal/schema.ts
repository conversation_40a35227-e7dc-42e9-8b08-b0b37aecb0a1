import * as v from "valibot";

export const CreateWarehouseSchema = v.object({
	name: v.pipe(
		v.string("Debe ingresar un nombre"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	code: v.pipe(
		v.string("Debe ingresar un código"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	type: v.pipe(
		v.string("Debe ingresar un tipo"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	category: v.pipe(
		v.string("Debe ingresar una categoría"),
		v.minLength(1, "Debe tener al menos un caracter"),
	),
	description: v.optional(v.nullable(v.string())),
	address: v.optional(v.nullable(v.string())),
	isActive: v.boolean(),
	isSystemWarehouse: v.boolean(),
});
export type CreateWarehouseSchema = v.InferOutput<typeof CreateWarehouseSchema>;
