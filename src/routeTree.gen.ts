/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

import { Route as rootRouteImport } from './routes/__root'
import { Route as LoginRouteImport } from './routes/login'
import { Route as AuthedRouteRouteImport } from './routes/_authed/route'
import { Route as IndexRouteImport } from './routes/index'
import { Route as AuthedAdminRouteRouteImport } from './routes/_authed/admin/route'
import { Route as AuthedAdminIndexRouteImport } from './routes/_authed/admin/index'
import { Route as AuthedAdminSecurityUsersIndexRouteImport } from './routes/_authed/admin/security/users/index'
import { Route as AuthedAdminProductsSuppliersIndexRouteImport } from './routes/_authed/admin/products/suppliers/index'
import { Route as AuthedAdminProductsRawMaterialsIndexRouteImport } from './routes/_authed/admin/products/raw-materials/index'
import { Route as AuthedAdminProductsProductsIndexRouteImport } from './routes/_authed/admin/products/products/index'
import { Route as AuthedAdminProductsProductionDevicesIndexRouteImport } from './routes/_authed/admin/products/production-devices/index'
import { Route as AuthedAdminProductsMeasurementUnitsIndexRouteImport } from './routes/_authed/admin/products/measurement-units/index'
import { Route as AuthedAdminProductsMaterialsIndexRouteImport } from './routes/_authed/admin/products/materials/index'
import { Route as AuthedAdminProductsCategoriesIndexRouteImport } from './routes/_authed/admin/products/categories/index'
import { Route as AuthedAdminProductsBrandsIndexRouteImport } from './routes/_authed/admin/products/brands/index'
import { Route as AuthedAdminManufactureWorkAreaIndexRouteImport } from './routes/_authed/admin/manufacture/work-area/index'
import { Route as AuthedAdminManufactureRecipesIndexRouteImport } from './routes/_authed/admin/manufacture/recipes/index'
import { Route as AuthedAdminManufactureProductionFlowIndexRouteImport } from './routes/_authed/admin/manufacture/production-flow/index'
import { Route as AuthedAdminManufactureOperationsIndexRouteImport } from './routes/_authed/admin/manufacture/operations/index'
import { Route as AuthedAdminInventoryWarehousesIndexRouteImport } from './routes/_authed/admin/inventory/warehouses/index'
import { Route as AuthedAdminProductsSuppliersCreateRouteImport } from './routes/_authed/admin/products/suppliers/create'
import { Route as AuthedAdminProductsRawMaterialsCreateRouteImport } from './routes/_authed/admin/products/raw-materials/create'
import { Route as AuthedAdminProductsProductsCreateRouteImport } from './routes/_authed/admin/products/products/create'
import { Route as AuthedAdminProductsProductionDevicesCreateRouteImport } from './routes/_authed/admin/products/production-devices/create'
import { Route as AuthedAdminProductsMaterialsCreateRouteImport } from './routes/_authed/admin/products/materials/create'
import { Route as AuthedAdminManufactureRecipesCreateRouteImport } from './routes/_authed/admin/manufacture/recipes/create'
import { Route as AuthedAdminManufactureProductionFlowCreateRouteImport } from './routes/_authed/admin/manufacture/production-flow/create'
import { Route as AuthedAdminProductsSuppliersEditIdRouteImport } from './routes/_authed/admin/products/suppliers/edit.$id'
import { Route as AuthedAdminProductsRawMaterialsEditIdRouteImport } from './routes/_authed/admin/products/raw-materials/edit.$id'
import { Route as AuthedAdminProductsProductsEditIdRouteImport } from './routes/_authed/admin/products/products/edit.$id'
import { Route as AuthedAdminProductsProductionDevicesEditIdRouteImport } from './routes/_authed/admin/products/production-devices/edit.$id'
import { Route as AuthedAdminProductsMaterialsEditIdRouteImport } from './routes/_authed/admin/products/materials/edit.$id'
import { Route as AuthedAdminManufactureRecipesEditIdRouteImport } from './routes/_authed/admin/manufacture/recipes/edit.$id'
import { Route as AuthedAdminManufactureProductionFlowEditIdRouteImport } from './routes/_authed/admin/manufacture/production-flow/edit.$id'

const LoginRoute = LoginRouteImport.update({
  id: '/login',
  path: '/login',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedRouteRoute = AuthedRouteRouteImport.update({
  id: '/_authed',
  getParentRoute: () => rootRouteImport,
} as any)
const IndexRoute = IndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => rootRouteImport,
} as any)
const AuthedAdminRouteRoute = AuthedAdminRouteRouteImport.update({
  id: '/admin',
  path: '/admin',
  getParentRoute: () => AuthedRouteRoute,
} as any)
const AuthedAdminIndexRoute = AuthedAdminIndexRouteImport.update({
  id: '/',
  path: '/',
  getParentRoute: () => AuthedAdminRouteRoute,
} as any)
const AuthedAdminSecurityUsersIndexRoute =
  AuthedAdminSecurityUsersIndexRouteImport.update({
    id: '/security/users/',
    path: '/security/users/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsSuppliersIndexRoute =
  AuthedAdminProductsSuppliersIndexRouteImport.update({
    id: '/products/suppliers/',
    path: '/products/suppliers/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsRawMaterialsIndexRoute =
  AuthedAdminProductsRawMaterialsIndexRouteImport.update({
    id: '/products/raw-materials/',
    path: '/products/raw-materials/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsProductsIndexRoute =
  AuthedAdminProductsProductsIndexRouteImport.update({
    id: '/products/products/',
    path: '/products/products/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsProductionDevicesIndexRoute =
  AuthedAdminProductsProductionDevicesIndexRouteImport.update({
    id: '/products/production-devices/',
    path: '/products/production-devices/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsMeasurementUnitsIndexRoute =
  AuthedAdminProductsMeasurementUnitsIndexRouteImport.update({
    id: '/products/measurement-units/',
    path: '/products/measurement-units/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsMaterialsIndexRoute =
  AuthedAdminProductsMaterialsIndexRouteImport.update({
    id: '/products/materials/',
    path: '/products/materials/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsCategoriesIndexRoute =
  AuthedAdminProductsCategoriesIndexRouteImport.update({
    id: '/products/categories/',
    path: '/products/categories/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsBrandsIndexRoute =
  AuthedAdminProductsBrandsIndexRouteImport.update({
    id: '/products/brands/',
    path: '/products/brands/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminManufactureWorkAreaIndexRoute =
  AuthedAdminManufactureWorkAreaIndexRouteImport.update({
    id: '/manufacture/work-area/',
    path: '/manufacture/work-area/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminManufactureRecipesIndexRoute =
  AuthedAdminManufactureRecipesIndexRouteImport.update({
    id: '/manufacture/recipes/',
    path: '/manufacture/recipes/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminManufactureProductionFlowIndexRoute =
  AuthedAdminManufactureProductionFlowIndexRouteImport.update({
    id: '/manufacture/production-flow/',
    path: '/manufacture/production-flow/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminManufactureOperationsIndexRoute =
  AuthedAdminManufactureOperationsIndexRouteImport.update({
    id: '/manufacture/operations/',
    path: '/manufacture/operations/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminInventoryWarehousesIndexRoute =
  AuthedAdminInventoryWarehousesIndexRouteImport.update({
    id: '/inventory/warehouses/',
    path: '/inventory/warehouses/',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsSuppliersCreateRoute =
  AuthedAdminProductsSuppliersCreateRouteImport.update({
    id: '/products/suppliers/create',
    path: '/products/suppliers/create',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsRawMaterialsCreateRoute =
  AuthedAdminProductsRawMaterialsCreateRouteImport.update({
    id: '/products/raw-materials/create',
    path: '/products/raw-materials/create',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsProductsCreateRoute =
  AuthedAdminProductsProductsCreateRouteImport.update({
    id: '/products/products/create',
    path: '/products/products/create',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsProductionDevicesCreateRoute =
  AuthedAdminProductsProductionDevicesCreateRouteImport.update({
    id: '/products/production-devices/create',
    path: '/products/production-devices/create',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsMaterialsCreateRoute =
  AuthedAdminProductsMaterialsCreateRouteImport.update({
    id: '/products/materials/create',
    path: '/products/materials/create',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminManufactureRecipesCreateRoute =
  AuthedAdminManufactureRecipesCreateRouteImport.update({
    id: '/manufacture/recipes/create',
    path: '/manufacture/recipes/create',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminManufactureProductionFlowCreateRoute =
  AuthedAdminManufactureProductionFlowCreateRouteImport.update({
    id: '/manufacture/production-flow/create',
    path: '/manufacture/production-flow/create',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsSuppliersEditIdRoute =
  AuthedAdminProductsSuppliersEditIdRouteImport.update({
    id: '/products/suppliers/edit/$id',
    path: '/products/suppliers/edit/$id',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsRawMaterialsEditIdRoute =
  AuthedAdminProductsRawMaterialsEditIdRouteImport.update({
    id: '/products/raw-materials/edit/$id',
    path: '/products/raw-materials/edit/$id',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsProductsEditIdRoute =
  AuthedAdminProductsProductsEditIdRouteImport.update({
    id: '/products/products/edit/$id',
    path: '/products/products/edit/$id',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsProductionDevicesEditIdRoute =
  AuthedAdminProductsProductionDevicesEditIdRouteImport.update({
    id: '/products/production-devices/edit/$id',
    path: '/products/production-devices/edit/$id',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminProductsMaterialsEditIdRoute =
  AuthedAdminProductsMaterialsEditIdRouteImport.update({
    id: '/products/materials/edit/$id',
    path: '/products/materials/edit/$id',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminManufactureRecipesEditIdRoute =
  AuthedAdminManufactureRecipesEditIdRouteImport.update({
    id: '/manufacture/recipes/edit/$id',
    path: '/manufacture/recipes/edit/$id',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)
const AuthedAdminManufactureProductionFlowEditIdRoute =
  AuthedAdminManufactureProductionFlowEditIdRouteImport.update({
    id: '/manufacture/production-flow/edit/$id',
    path: '/manufacture/production-flow/edit/$id',
    getParentRoute: () => AuthedAdminRouteRoute,
  } as any)

export interface FileRoutesByFullPath {
  '/': typeof IndexRoute
  '': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/admin': typeof AuthedAdminRouteRouteWithChildren
  '/admin/': typeof AuthedAdminIndexRoute
  '/admin/manufacture/production-flow/create': typeof AuthedAdminManufactureProductionFlowCreateRoute
  '/admin/manufacture/recipes/create': typeof AuthedAdminManufactureRecipesCreateRoute
  '/admin/products/materials/create': typeof AuthedAdminProductsMaterialsCreateRoute
  '/admin/products/production-devices/create': typeof AuthedAdminProductsProductionDevicesCreateRoute
  '/admin/products/products/create': typeof AuthedAdminProductsProductsCreateRoute
  '/admin/products/raw-materials/create': typeof AuthedAdminProductsRawMaterialsCreateRoute
  '/admin/products/suppliers/create': typeof AuthedAdminProductsSuppliersCreateRoute
  '/admin/inventory/warehouses': typeof AuthedAdminInventoryWarehousesIndexRoute
  '/admin/manufacture/operations': typeof AuthedAdminManufactureOperationsIndexRoute
  '/admin/manufacture/production-flow': typeof AuthedAdminManufactureProductionFlowIndexRoute
  '/admin/manufacture/recipes': typeof AuthedAdminManufactureRecipesIndexRoute
  '/admin/manufacture/work-area': typeof AuthedAdminManufactureWorkAreaIndexRoute
  '/admin/products/brands': typeof AuthedAdminProductsBrandsIndexRoute
  '/admin/products/categories': typeof AuthedAdminProductsCategoriesIndexRoute
  '/admin/products/materials': typeof AuthedAdminProductsMaterialsIndexRoute
  '/admin/products/measurement-units': typeof AuthedAdminProductsMeasurementUnitsIndexRoute
  '/admin/products/production-devices': typeof AuthedAdminProductsProductionDevicesIndexRoute
  '/admin/products/products': typeof AuthedAdminProductsProductsIndexRoute
  '/admin/products/raw-materials': typeof AuthedAdminProductsRawMaterialsIndexRoute
  '/admin/products/suppliers': typeof AuthedAdminProductsSuppliersIndexRoute
  '/admin/security/users': typeof AuthedAdminSecurityUsersIndexRoute
  '/admin/manufacture/production-flow/edit/$id': typeof AuthedAdminManufactureProductionFlowEditIdRoute
  '/admin/manufacture/recipes/edit/$id': typeof AuthedAdminManufactureRecipesEditIdRoute
  '/admin/products/materials/edit/$id': typeof AuthedAdminProductsMaterialsEditIdRoute
  '/admin/products/production-devices/edit/$id': typeof AuthedAdminProductsProductionDevicesEditIdRoute
  '/admin/products/products/edit/$id': typeof AuthedAdminProductsProductsEditIdRoute
  '/admin/products/raw-materials/edit/$id': typeof AuthedAdminProductsRawMaterialsEditIdRoute
  '/admin/products/suppliers/edit/$id': typeof AuthedAdminProductsSuppliersEditIdRoute
}
export interface FileRoutesByTo {
  '/': typeof IndexRoute
  '': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/admin': typeof AuthedAdminIndexRoute
  '/admin/manufacture/production-flow/create': typeof AuthedAdminManufactureProductionFlowCreateRoute
  '/admin/manufacture/recipes/create': typeof AuthedAdminManufactureRecipesCreateRoute
  '/admin/products/materials/create': typeof AuthedAdminProductsMaterialsCreateRoute
  '/admin/products/production-devices/create': typeof AuthedAdminProductsProductionDevicesCreateRoute
  '/admin/products/products/create': typeof AuthedAdminProductsProductsCreateRoute
  '/admin/products/raw-materials/create': typeof AuthedAdminProductsRawMaterialsCreateRoute
  '/admin/products/suppliers/create': typeof AuthedAdminProductsSuppliersCreateRoute
  '/admin/inventory/warehouses': typeof AuthedAdminInventoryWarehousesIndexRoute
  '/admin/manufacture/operations': typeof AuthedAdminManufactureOperationsIndexRoute
  '/admin/manufacture/production-flow': typeof AuthedAdminManufactureProductionFlowIndexRoute
  '/admin/manufacture/recipes': typeof AuthedAdminManufactureRecipesIndexRoute
  '/admin/manufacture/work-area': typeof AuthedAdminManufactureWorkAreaIndexRoute
  '/admin/products/brands': typeof AuthedAdminProductsBrandsIndexRoute
  '/admin/products/categories': typeof AuthedAdminProductsCategoriesIndexRoute
  '/admin/products/materials': typeof AuthedAdminProductsMaterialsIndexRoute
  '/admin/products/measurement-units': typeof AuthedAdminProductsMeasurementUnitsIndexRoute
  '/admin/products/production-devices': typeof AuthedAdminProductsProductionDevicesIndexRoute
  '/admin/products/products': typeof AuthedAdminProductsProductsIndexRoute
  '/admin/products/raw-materials': typeof AuthedAdminProductsRawMaterialsIndexRoute
  '/admin/products/suppliers': typeof AuthedAdminProductsSuppliersIndexRoute
  '/admin/security/users': typeof AuthedAdminSecurityUsersIndexRoute
  '/admin/manufacture/production-flow/edit/$id': typeof AuthedAdminManufactureProductionFlowEditIdRoute
  '/admin/manufacture/recipes/edit/$id': typeof AuthedAdminManufactureRecipesEditIdRoute
  '/admin/products/materials/edit/$id': typeof AuthedAdminProductsMaterialsEditIdRoute
  '/admin/products/production-devices/edit/$id': typeof AuthedAdminProductsProductionDevicesEditIdRoute
  '/admin/products/products/edit/$id': typeof AuthedAdminProductsProductsEditIdRoute
  '/admin/products/raw-materials/edit/$id': typeof AuthedAdminProductsRawMaterialsEditIdRoute
  '/admin/products/suppliers/edit/$id': typeof AuthedAdminProductsSuppliersEditIdRoute
}
export interface FileRoutesById {
  __root__: typeof rootRouteImport
  '/': typeof IndexRoute
  '/_authed': typeof AuthedRouteRouteWithChildren
  '/login': typeof LoginRoute
  '/_authed/admin': typeof AuthedAdminRouteRouteWithChildren
  '/_authed/admin/': typeof AuthedAdminIndexRoute
  '/_authed/admin/manufacture/production-flow/create': typeof AuthedAdminManufactureProductionFlowCreateRoute
  '/_authed/admin/manufacture/recipes/create': typeof AuthedAdminManufactureRecipesCreateRoute
  '/_authed/admin/products/materials/create': typeof AuthedAdminProductsMaterialsCreateRoute
  '/_authed/admin/products/production-devices/create': typeof AuthedAdminProductsProductionDevicesCreateRoute
  '/_authed/admin/products/products/create': typeof AuthedAdminProductsProductsCreateRoute
  '/_authed/admin/products/raw-materials/create': typeof AuthedAdminProductsRawMaterialsCreateRoute
  '/_authed/admin/products/suppliers/create': typeof AuthedAdminProductsSuppliersCreateRoute
  '/_authed/admin/inventory/warehouses/': typeof AuthedAdminInventoryWarehousesIndexRoute
  '/_authed/admin/manufacture/operations/': typeof AuthedAdminManufactureOperationsIndexRoute
  '/_authed/admin/manufacture/production-flow/': typeof AuthedAdminManufactureProductionFlowIndexRoute
  '/_authed/admin/manufacture/recipes/': typeof AuthedAdminManufactureRecipesIndexRoute
  '/_authed/admin/manufacture/work-area/': typeof AuthedAdminManufactureWorkAreaIndexRoute
  '/_authed/admin/products/brands/': typeof AuthedAdminProductsBrandsIndexRoute
  '/_authed/admin/products/categories/': typeof AuthedAdminProductsCategoriesIndexRoute
  '/_authed/admin/products/materials/': typeof AuthedAdminProductsMaterialsIndexRoute
  '/_authed/admin/products/measurement-units/': typeof AuthedAdminProductsMeasurementUnitsIndexRoute
  '/_authed/admin/products/production-devices/': typeof AuthedAdminProductsProductionDevicesIndexRoute
  '/_authed/admin/products/products/': typeof AuthedAdminProductsProductsIndexRoute
  '/_authed/admin/products/raw-materials/': typeof AuthedAdminProductsRawMaterialsIndexRoute
  '/_authed/admin/products/suppliers/': typeof AuthedAdminProductsSuppliersIndexRoute
  '/_authed/admin/security/users/': typeof AuthedAdminSecurityUsersIndexRoute
  '/_authed/admin/manufacture/production-flow/edit/$id': typeof AuthedAdminManufactureProductionFlowEditIdRoute
  '/_authed/admin/manufacture/recipes/edit/$id': typeof AuthedAdminManufactureRecipesEditIdRoute
  '/_authed/admin/products/materials/edit/$id': typeof AuthedAdminProductsMaterialsEditIdRoute
  '/_authed/admin/products/production-devices/edit/$id': typeof AuthedAdminProductsProductionDevicesEditIdRoute
  '/_authed/admin/products/products/edit/$id': typeof AuthedAdminProductsProductsEditIdRoute
  '/_authed/admin/products/raw-materials/edit/$id': typeof AuthedAdminProductsRawMaterialsEditIdRoute
  '/_authed/admin/products/suppliers/edit/$id': typeof AuthedAdminProductsSuppliersEditIdRoute
}
export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath
  fullPaths:
    | '/'
    | ''
    | '/login'
    | '/admin'
    | '/admin/'
    | '/admin/manufacture/production-flow/create'
    | '/admin/manufacture/recipes/create'
    | '/admin/products/materials/create'
    | '/admin/products/production-devices/create'
    | '/admin/products/products/create'
    | '/admin/products/raw-materials/create'
    | '/admin/products/suppliers/create'
    | '/admin/inventory/warehouses'
    | '/admin/manufacture/operations'
    | '/admin/manufacture/production-flow'
    | '/admin/manufacture/recipes'
    | '/admin/manufacture/work-area'
    | '/admin/products/brands'
    | '/admin/products/categories'
    | '/admin/products/materials'
    | '/admin/products/measurement-units'
    | '/admin/products/production-devices'
    | '/admin/products/products'
    | '/admin/products/raw-materials'
    | '/admin/products/suppliers'
    | '/admin/security/users'
    | '/admin/manufacture/production-flow/edit/$id'
    | '/admin/manufacture/recipes/edit/$id'
    | '/admin/products/materials/edit/$id'
    | '/admin/products/production-devices/edit/$id'
    | '/admin/products/products/edit/$id'
    | '/admin/products/raw-materials/edit/$id'
    | '/admin/products/suppliers/edit/$id'
  fileRoutesByTo: FileRoutesByTo
  to:
    | '/'
    | ''
    | '/login'
    | '/admin'
    | '/admin/manufacture/production-flow/create'
    | '/admin/manufacture/recipes/create'
    | '/admin/products/materials/create'
    | '/admin/products/production-devices/create'
    | '/admin/products/products/create'
    | '/admin/products/raw-materials/create'
    | '/admin/products/suppliers/create'
    | '/admin/inventory/warehouses'
    | '/admin/manufacture/operations'
    | '/admin/manufacture/production-flow'
    | '/admin/manufacture/recipes'
    | '/admin/manufacture/work-area'
    | '/admin/products/brands'
    | '/admin/products/categories'
    | '/admin/products/materials'
    | '/admin/products/measurement-units'
    | '/admin/products/production-devices'
    | '/admin/products/products'
    | '/admin/products/raw-materials'
    | '/admin/products/suppliers'
    | '/admin/security/users'
    | '/admin/manufacture/production-flow/edit/$id'
    | '/admin/manufacture/recipes/edit/$id'
    | '/admin/products/materials/edit/$id'
    | '/admin/products/production-devices/edit/$id'
    | '/admin/products/products/edit/$id'
    | '/admin/products/raw-materials/edit/$id'
    | '/admin/products/suppliers/edit/$id'
  id:
    | '__root__'
    | '/'
    | '/_authed'
    | '/login'
    | '/_authed/admin'
    | '/_authed/admin/'
    | '/_authed/admin/manufacture/production-flow/create'
    | '/_authed/admin/manufacture/recipes/create'
    | '/_authed/admin/products/materials/create'
    | '/_authed/admin/products/production-devices/create'
    | '/_authed/admin/products/products/create'
    | '/_authed/admin/products/raw-materials/create'
    | '/_authed/admin/products/suppliers/create'
    | '/_authed/admin/inventory/warehouses/'
    | '/_authed/admin/manufacture/operations/'
    | '/_authed/admin/manufacture/production-flow/'
    | '/_authed/admin/manufacture/recipes/'
    | '/_authed/admin/manufacture/work-area/'
    | '/_authed/admin/products/brands/'
    | '/_authed/admin/products/categories/'
    | '/_authed/admin/products/materials/'
    | '/_authed/admin/products/measurement-units/'
    | '/_authed/admin/products/production-devices/'
    | '/_authed/admin/products/products/'
    | '/_authed/admin/products/raw-materials/'
    | '/_authed/admin/products/suppliers/'
    | '/_authed/admin/security/users/'
    | '/_authed/admin/manufacture/production-flow/edit/$id'
    | '/_authed/admin/manufacture/recipes/edit/$id'
    | '/_authed/admin/products/materials/edit/$id'
    | '/_authed/admin/products/production-devices/edit/$id'
    | '/_authed/admin/products/products/edit/$id'
    | '/_authed/admin/products/raw-materials/edit/$id'
    | '/_authed/admin/products/suppliers/edit/$id'
  fileRoutesById: FileRoutesById
}
export interface RootRouteChildren {
  IndexRoute: typeof IndexRoute
  AuthedRouteRoute: typeof AuthedRouteRouteWithChildren
  LoginRoute: typeof LoginRoute
}

declare module '@tanstack/react-router' {
  interface FileRoutesByPath {
    '/': {
      id: '/'
      path: '/'
      fullPath: '/'
      preLoaderRoute: typeof IndexRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed': {
      id: '/_authed'
      path: ''
      fullPath: ''
      preLoaderRoute: typeof AuthedRouteRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/login': {
      id: '/login'
      path: '/login'
      fullPath: '/login'
      preLoaderRoute: typeof LoginRouteImport
      parentRoute: typeof rootRouteImport
    }
    '/_authed/admin': {
      id: '/_authed/admin'
      path: '/admin'
      fullPath: '/admin'
      preLoaderRoute: typeof AuthedAdminRouteRouteImport
      parentRoute: typeof AuthedRouteRoute
    }
    '/_authed/admin/': {
      id: '/_authed/admin/'
      path: '/'
      fullPath: '/admin/'
      preLoaderRoute: typeof AuthedAdminIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/manufacture/production-flow/create': {
      id: '/_authed/admin/manufacture/production-flow/create'
      path: '/manufacture/production-flow/create'
      fullPath: '/admin/manufacture/production-flow/create'
      preLoaderRoute: typeof AuthedAdminManufactureProductionFlowCreateRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/manufacture/recipes/create': {
      id: '/_authed/admin/manufacture/recipes/create'
      path: '/manufacture/recipes/create'
      fullPath: '/admin/manufacture/recipes/create'
      preLoaderRoute: typeof AuthedAdminManufactureRecipesCreateRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/materials/create': {
      id: '/_authed/admin/products/materials/create'
      path: '/products/materials/create'
      fullPath: '/admin/products/materials/create'
      preLoaderRoute: typeof AuthedAdminProductsMaterialsCreateRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/production-devices/create': {
      id: '/_authed/admin/products/production-devices/create'
      path: '/products/production-devices/create'
      fullPath: '/admin/products/production-devices/create'
      preLoaderRoute: typeof AuthedAdminProductsProductionDevicesCreateRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/products/create': {
      id: '/_authed/admin/products/products/create'
      path: '/products/products/create'
      fullPath: '/admin/products/products/create'
      preLoaderRoute: typeof AuthedAdminProductsProductsCreateRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/raw-materials/create': {
      id: '/_authed/admin/products/raw-materials/create'
      path: '/products/raw-materials/create'
      fullPath: '/admin/products/raw-materials/create'
      preLoaderRoute: typeof AuthedAdminProductsRawMaterialsCreateRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/suppliers/create': {
      id: '/_authed/admin/products/suppliers/create'
      path: '/products/suppliers/create'
      fullPath: '/admin/products/suppliers/create'
      preLoaderRoute: typeof AuthedAdminProductsSuppliersCreateRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/inventory/warehouses/': {
      id: '/_authed/admin/inventory/warehouses/'
      path: '/inventory/warehouses'
      fullPath: '/admin/inventory/warehouses'
      preLoaderRoute: typeof AuthedAdminInventoryWarehousesIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/manufacture/operations/': {
      id: '/_authed/admin/manufacture/operations/'
      path: '/manufacture/operations'
      fullPath: '/admin/manufacture/operations'
      preLoaderRoute: typeof AuthedAdminManufactureOperationsIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/manufacture/production-flow/': {
      id: '/_authed/admin/manufacture/production-flow/'
      path: '/manufacture/production-flow'
      fullPath: '/admin/manufacture/production-flow'
      preLoaderRoute: typeof AuthedAdminManufactureProductionFlowIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/manufacture/recipes/': {
      id: '/_authed/admin/manufacture/recipes/'
      path: '/manufacture/recipes'
      fullPath: '/admin/manufacture/recipes'
      preLoaderRoute: typeof AuthedAdminManufactureRecipesIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/manufacture/work-area/': {
      id: '/_authed/admin/manufacture/work-area/'
      path: '/manufacture/work-area'
      fullPath: '/admin/manufacture/work-area'
      preLoaderRoute: typeof AuthedAdminManufactureWorkAreaIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/brands/': {
      id: '/_authed/admin/products/brands/'
      path: '/products/brands'
      fullPath: '/admin/products/brands'
      preLoaderRoute: typeof AuthedAdminProductsBrandsIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/categories/': {
      id: '/_authed/admin/products/categories/'
      path: '/products/categories'
      fullPath: '/admin/products/categories'
      preLoaderRoute: typeof AuthedAdminProductsCategoriesIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/materials/': {
      id: '/_authed/admin/products/materials/'
      path: '/products/materials'
      fullPath: '/admin/products/materials'
      preLoaderRoute: typeof AuthedAdminProductsMaterialsIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/measurement-units/': {
      id: '/_authed/admin/products/measurement-units/'
      path: '/products/measurement-units'
      fullPath: '/admin/products/measurement-units'
      preLoaderRoute: typeof AuthedAdminProductsMeasurementUnitsIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/production-devices/': {
      id: '/_authed/admin/products/production-devices/'
      path: '/products/production-devices'
      fullPath: '/admin/products/production-devices'
      preLoaderRoute: typeof AuthedAdminProductsProductionDevicesIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/products/': {
      id: '/_authed/admin/products/products/'
      path: '/products/products'
      fullPath: '/admin/products/products'
      preLoaderRoute: typeof AuthedAdminProductsProductsIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/raw-materials/': {
      id: '/_authed/admin/products/raw-materials/'
      path: '/products/raw-materials'
      fullPath: '/admin/products/raw-materials'
      preLoaderRoute: typeof AuthedAdminProductsRawMaterialsIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/suppliers/': {
      id: '/_authed/admin/products/suppliers/'
      path: '/products/suppliers'
      fullPath: '/admin/products/suppliers'
      preLoaderRoute: typeof AuthedAdminProductsSuppliersIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/security/users/': {
      id: '/_authed/admin/security/users/'
      path: '/security/users'
      fullPath: '/admin/security/users'
      preLoaderRoute: typeof AuthedAdminSecurityUsersIndexRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/manufacture/production-flow/edit/$id': {
      id: '/_authed/admin/manufacture/production-flow/edit/$id'
      path: '/manufacture/production-flow/edit/$id'
      fullPath: '/admin/manufacture/production-flow/edit/$id'
      preLoaderRoute: typeof AuthedAdminManufactureProductionFlowEditIdRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/manufacture/recipes/edit/$id': {
      id: '/_authed/admin/manufacture/recipes/edit/$id'
      path: '/manufacture/recipes/edit/$id'
      fullPath: '/admin/manufacture/recipes/edit/$id'
      preLoaderRoute: typeof AuthedAdminManufactureRecipesEditIdRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/materials/edit/$id': {
      id: '/_authed/admin/products/materials/edit/$id'
      path: '/products/materials/edit/$id'
      fullPath: '/admin/products/materials/edit/$id'
      preLoaderRoute: typeof AuthedAdminProductsMaterialsEditIdRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/production-devices/edit/$id': {
      id: '/_authed/admin/products/production-devices/edit/$id'
      path: '/products/production-devices/edit/$id'
      fullPath: '/admin/products/production-devices/edit/$id'
      preLoaderRoute: typeof AuthedAdminProductsProductionDevicesEditIdRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/products/edit/$id': {
      id: '/_authed/admin/products/products/edit/$id'
      path: '/products/products/edit/$id'
      fullPath: '/admin/products/products/edit/$id'
      preLoaderRoute: typeof AuthedAdminProductsProductsEditIdRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/raw-materials/edit/$id': {
      id: '/_authed/admin/products/raw-materials/edit/$id'
      path: '/products/raw-materials/edit/$id'
      fullPath: '/admin/products/raw-materials/edit/$id'
      preLoaderRoute: typeof AuthedAdminProductsRawMaterialsEditIdRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
    '/_authed/admin/products/suppliers/edit/$id': {
      id: '/_authed/admin/products/suppliers/edit/$id'
      path: '/products/suppliers/edit/$id'
      fullPath: '/admin/products/suppliers/edit/$id'
      preLoaderRoute: typeof AuthedAdminProductsSuppliersEditIdRouteImport
      parentRoute: typeof AuthedAdminRouteRoute
    }
  }
}

interface AuthedAdminRouteRouteChildren {
  AuthedAdminIndexRoute: typeof AuthedAdminIndexRoute
  AuthedAdminManufactureProductionFlowCreateRoute: typeof AuthedAdminManufactureProductionFlowCreateRoute
  AuthedAdminManufactureRecipesCreateRoute: typeof AuthedAdminManufactureRecipesCreateRoute
  AuthedAdminProductsMaterialsCreateRoute: typeof AuthedAdminProductsMaterialsCreateRoute
  AuthedAdminProductsProductionDevicesCreateRoute: typeof AuthedAdminProductsProductionDevicesCreateRoute
  AuthedAdminProductsProductsCreateRoute: typeof AuthedAdminProductsProductsCreateRoute
  AuthedAdminProductsRawMaterialsCreateRoute: typeof AuthedAdminProductsRawMaterialsCreateRoute
  AuthedAdminProductsSuppliersCreateRoute: typeof AuthedAdminProductsSuppliersCreateRoute
  AuthedAdminInventoryWarehousesIndexRoute: typeof AuthedAdminInventoryWarehousesIndexRoute
  AuthedAdminManufactureOperationsIndexRoute: typeof AuthedAdminManufactureOperationsIndexRoute
  AuthedAdminManufactureProductionFlowIndexRoute: typeof AuthedAdminManufactureProductionFlowIndexRoute
  AuthedAdminManufactureRecipesIndexRoute: typeof AuthedAdminManufactureRecipesIndexRoute
  AuthedAdminManufactureWorkAreaIndexRoute: typeof AuthedAdminManufactureWorkAreaIndexRoute
  AuthedAdminProductsBrandsIndexRoute: typeof AuthedAdminProductsBrandsIndexRoute
  AuthedAdminProductsCategoriesIndexRoute: typeof AuthedAdminProductsCategoriesIndexRoute
  AuthedAdminProductsMaterialsIndexRoute: typeof AuthedAdminProductsMaterialsIndexRoute
  AuthedAdminProductsMeasurementUnitsIndexRoute: typeof AuthedAdminProductsMeasurementUnitsIndexRoute
  AuthedAdminProductsProductionDevicesIndexRoute: typeof AuthedAdminProductsProductionDevicesIndexRoute
  AuthedAdminProductsProductsIndexRoute: typeof AuthedAdminProductsProductsIndexRoute
  AuthedAdminProductsRawMaterialsIndexRoute: typeof AuthedAdminProductsRawMaterialsIndexRoute
  AuthedAdminProductsSuppliersIndexRoute: typeof AuthedAdminProductsSuppliersIndexRoute
  AuthedAdminSecurityUsersIndexRoute: typeof AuthedAdminSecurityUsersIndexRoute
  AuthedAdminManufactureProductionFlowEditIdRoute: typeof AuthedAdminManufactureProductionFlowEditIdRoute
  AuthedAdminManufactureRecipesEditIdRoute: typeof AuthedAdminManufactureRecipesEditIdRoute
  AuthedAdminProductsMaterialsEditIdRoute: typeof AuthedAdminProductsMaterialsEditIdRoute
  AuthedAdminProductsProductionDevicesEditIdRoute: typeof AuthedAdminProductsProductionDevicesEditIdRoute
  AuthedAdminProductsProductsEditIdRoute: typeof AuthedAdminProductsProductsEditIdRoute
  AuthedAdminProductsRawMaterialsEditIdRoute: typeof AuthedAdminProductsRawMaterialsEditIdRoute
  AuthedAdminProductsSuppliersEditIdRoute: typeof AuthedAdminProductsSuppliersEditIdRoute
}

const AuthedAdminRouteRouteChildren: AuthedAdminRouteRouteChildren = {
  AuthedAdminIndexRoute: AuthedAdminIndexRoute,
  AuthedAdminManufactureProductionFlowCreateRoute:
    AuthedAdminManufactureProductionFlowCreateRoute,
  AuthedAdminManufactureRecipesCreateRoute:
    AuthedAdminManufactureRecipesCreateRoute,
  AuthedAdminProductsMaterialsCreateRoute:
    AuthedAdminProductsMaterialsCreateRoute,
  AuthedAdminProductsProductionDevicesCreateRoute:
    AuthedAdminProductsProductionDevicesCreateRoute,
  AuthedAdminProductsProductsCreateRoute:
    AuthedAdminProductsProductsCreateRoute,
  AuthedAdminProductsRawMaterialsCreateRoute:
    AuthedAdminProductsRawMaterialsCreateRoute,
  AuthedAdminProductsSuppliersCreateRoute:
    AuthedAdminProductsSuppliersCreateRoute,
  AuthedAdminInventoryWarehousesIndexRoute:
    AuthedAdminInventoryWarehousesIndexRoute,
  AuthedAdminManufactureOperationsIndexRoute:
    AuthedAdminManufactureOperationsIndexRoute,
  AuthedAdminManufactureProductionFlowIndexRoute:
    AuthedAdminManufactureProductionFlowIndexRoute,
  AuthedAdminManufactureRecipesIndexRoute:
    AuthedAdminManufactureRecipesIndexRoute,
  AuthedAdminManufactureWorkAreaIndexRoute:
    AuthedAdminManufactureWorkAreaIndexRoute,
  AuthedAdminProductsBrandsIndexRoute: AuthedAdminProductsBrandsIndexRoute,
  AuthedAdminProductsCategoriesIndexRoute:
    AuthedAdminProductsCategoriesIndexRoute,
  AuthedAdminProductsMaterialsIndexRoute:
    AuthedAdminProductsMaterialsIndexRoute,
  AuthedAdminProductsMeasurementUnitsIndexRoute:
    AuthedAdminProductsMeasurementUnitsIndexRoute,
  AuthedAdminProductsProductionDevicesIndexRoute:
    AuthedAdminProductsProductionDevicesIndexRoute,
  AuthedAdminProductsProductsIndexRoute: AuthedAdminProductsProductsIndexRoute,
  AuthedAdminProductsRawMaterialsIndexRoute:
    AuthedAdminProductsRawMaterialsIndexRoute,
  AuthedAdminProductsSuppliersIndexRoute:
    AuthedAdminProductsSuppliersIndexRoute,
  AuthedAdminSecurityUsersIndexRoute: AuthedAdminSecurityUsersIndexRoute,
  AuthedAdminManufactureProductionFlowEditIdRoute:
    AuthedAdminManufactureProductionFlowEditIdRoute,
  AuthedAdminManufactureRecipesEditIdRoute:
    AuthedAdminManufactureRecipesEditIdRoute,
  AuthedAdminProductsMaterialsEditIdRoute:
    AuthedAdminProductsMaterialsEditIdRoute,
  AuthedAdminProductsProductionDevicesEditIdRoute:
    AuthedAdminProductsProductionDevicesEditIdRoute,
  AuthedAdminProductsProductsEditIdRoute:
    AuthedAdminProductsProductsEditIdRoute,
  AuthedAdminProductsRawMaterialsEditIdRoute:
    AuthedAdminProductsRawMaterialsEditIdRoute,
  AuthedAdminProductsSuppliersEditIdRoute:
    AuthedAdminProductsSuppliersEditIdRoute,
}

const AuthedAdminRouteRouteWithChildren =
  AuthedAdminRouteRoute._addFileChildren(AuthedAdminRouteRouteChildren)

interface AuthedRouteRouteChildren {
  AuthedAdminRouteRoute: typeof AuthedAdminRouteRouteWithChildren
}

const AuthedRouteRouteChildren: AuthedRouteRouteChildren = {
  AuthedAdminRouteRoute: AuthedAdminRouteRouteWithChildren,
}

const AuthedRouteRouteWithChildren = AuthedRouteRoute._addFileChildren(
  AuthedRouteRouteChildren,
)

const rootRouteChildren: RootRouteChildren = {
  IndexRoute: IndexRoute,
  AuthedRouteRoute: AuthedRouteRouteWithChildren,
  LoginRoute: LoginRoute,
}
export const routeTree = rootRouteImport
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>()
